package net.armcloud.paascenter.common.model.bo.task;

import lombok.Data;

import java.util.List;

/**
 * Pad任务业务对象
 */
@Data
public class PadTaskBO {
    
    /**
     * 主任务ID
     */
    private Long masterTaskId;
    
    /**
     * 主任务唯一ID
     */
    private String masterUniqueId;
    
    /**
     * 子任务列表
     */
    private List<PadSubTaskBO> subTasks;
    
    /**
     * 子任务业务对象
     */
    @Data
    public static class PadSubTaskBO {
        
        /**
         * Pad编码
         */
        private String padCode;
        
        /**
         * 客户任务ID
         */
        private Integer customerTaskId;
        
        /**
         * 发送命令是否成功
         */
        private Boolean sendCmdSuccess;
        
        /**
         * 子任务ID
         */
        private Long subTaskId;
        
        /**
         * 子任务唯一ID
         */
        private String subTaskUniqueId;
        
        /**
         * 子任务状态
         */
        private Integer subTaskStatus;
        
        /**
         * 错误信息
         */
        private String errMsg;
        
        /**
         * 是否在线
         */
        private Boolean online;
    }
}
