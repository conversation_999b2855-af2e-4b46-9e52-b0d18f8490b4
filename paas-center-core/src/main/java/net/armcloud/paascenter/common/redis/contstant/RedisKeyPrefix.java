package net.armcloud.paascenter.common.redis.contstant;

/**
 * redis key的前缀
 */
public class RedisKeyPrefix {
    //客户密钥信息
    public static final String CUSTOMER_ACCESS = "customer_access:";
    //客户密钥信息
    public static final String CUSTOMER_ACCESS_ID = "customer_access_id:";
    //sdk token信息
    public static final String SDK_TOKEN = "sdk_token:";
    //sdk token与client_uuid绑定key
    public static final String SDK_TOKEN_CLIENT_UUID = "sdk_token_uuid:";
    //云机生成
    public static final String DEVICE_CODE_DC = "device_code_dc:";
    //客户回调地址
    public static final String CUSTOMER_CALLBACK_URL = "customer_callback_url:";
    //云机状态变更消息消费锁
    public static final String VCP_PAD_STATUS_MSG_LOCK = "vcp_pad_status_msg_lock:";
    //云机属性是否已上报
    public static final String PAD_PROPERTIES_COUNT = "pad_properties_count:";
    //云机属性分类
    public static final String PROPERTIES_KEY = "pad_properties_key:";
    //客户任务ID锁
    public static final String CUSTOMER_TASK_ID_KEY = "customer_task_id_key_";
    //客户应用ID锁
    public static final String CUSTOMER_APP_ID_KEY = "customer_app_id_key_";
    //云机流量数据消费锁
    public static final String PAD_TRAFFIC_INFO_MSG_LOCK = "pad_traffic_info_msg_lock:";
    //外部云机编号对应的云机信息
    public static final String PAD_OUT_CODE_KEY = "pad_out_code:";
    //物理机状态变更消息消费锁
    public static final String VCP_DEVICE_STATUS_MSG_LOCK = "vcp_device_status_msg_lock:";
    //外部云机编号对应的机房信息
    public static final String DC_INFO_KEY = "dc_info:";
    //bmc用户token
    public static final String BMC_USER_TOKEN = "bmc_user_token:";

    public static final String VIRTUALIZE_DEVICE_TASK_RESULT_MSG_LOCK = "virtualize_device_task_result_msg_lock:";
    public static final String VIRTUALIZE_DEVICE_STATUS_MSG_LOCK = "virtualize_device_status_msg_lock:";
    //推送镜像任务结果消息消费锁
    public static final String PUSH_IMAGE_TASK_RESULT_MSG_LOCK = "push_image_task_result_msg_lock:";

    public static final String OLD_GENERATE_PREVIEW = "old_generate_preview:";
    //云机拉流数据消费锁
    public static final String PAD_PULL_FLOW_INFO_MSG_LOCK = "pad_pull_flow_info_msg_lock:";

    public static final String PAD_IP_LOCK = "pad_ip_lock:";

    //云机系统数据消费锁
    public static final String PAD_SYSTEM_CONFIG_DATA_MSG_LOCK = "pad_system_config_data_msg_lock:";
    //默认root应用配置
    public static final String DEFAULT_ROOT_APP_CONFIG = "default_root_app_config:";

    //cbs最新文件
    public static final String CBS_LAST_FILE = "cbs_last_file:";
    /**
     * 管理员角色组前缀
     */
    public static final String USER_ROLES_PREFIX = "USER:ROLES:";

    //上传镜像版本序号缓存
    public static final String CUSTOMER_IMAGE_TAG_SERIAL_NO = "customer_image_tag_serial_no:";

    /**新实例编号第一位随机数*/
    public static final String NEW_PAD_CODE_RAMDOM_FIRST = "new_pad_code_ramdom_first:";

    /**新实例编号第一位随机数 锁*/
    public static final String NEW_PAD_CODE_RAMDOM_FIRST_LOCK = "new_pad_code_ramdom_first_lock:";
}
