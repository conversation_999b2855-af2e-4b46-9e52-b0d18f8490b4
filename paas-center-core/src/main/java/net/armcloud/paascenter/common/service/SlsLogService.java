package net.armcloud.paascenter.common.service;

import cn.hutool.core.date.DateUtil;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.request.PutLogsRequest;
import com.aliyun.openservices.log.response.PutLogsResponse;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.config.SlsLogConfig;
import net.armcloud.paascenter.common.log.SlsServiceHolder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 阿里云SLS日志服务
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "aliyun.sls", name = "enabled", havingValue = "true")
public class SlsLogService {

    @Resource
    private SlsLogConfig slsLogConfig;

    private Client slsClient;
    private BlockingQueue<LogItem> logQueue;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private ScheduledExecutorService scheduledExecutor;

    @PostConstruct
    public void init() {
        try {
            // 初始化有界队列，防止内存溢出
            logQueue = new LinkedBlockingQueue<>(slsLogConfig.getQueueCapacity());

            // 初始化SLS客户端
            slsClient = new Client(
                slsLogConfig.getEndpoint(),
                slsLogConfig.getAccessKeyId(),
                slsLogConfig.getAccessKeySecret()
            );

            // 启动多线程发送任务 - 高并发优化
            int threadCount = Math.max(4, Runtime.getRuntime().availableProcessors());
            scheduledExecutor = new ScheduledThreadPoolExecutor(threadCount, r -> {
                Thread thread = new Thread(r, "sls-log-sender");
                thread.setDaemon(true);
                return thread;
            });

            running.set(true);

            // 启动多个发送线程
            for (int i = 0; i < threadCount; i++) {
                scheduledExecutor.scheduleWithFixedDelay(this::sendBatchLogs,
                    i * 100, slsLogConfig.getLingerMs(), TimeUnit.MILLISECONDS);
            }

            // 将自己注册到SlsServiceHolder中，供Logback Appender使用
            SlsServiceHolder.setSlsLogService(this);

            log.info("SLS日志服务初始化成功，项目: {}, 日志库: {}",
                slsLogConfig.getProject(), slsLogConfig.getLogstore());
        } catch (Exception e) {
            log.error("SLS日志服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        running.set(false);
        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 发送剩余日志
        sendBatchLogs();
        
        if (slsClient != null) {
            slsClient.shutdown();
        }
        log.info("SLS日志服务已关闭");
    }

    /**
     * 异步发送日志
     */
    @Async
    public void sendLogAsync(String level, String logger, String message, String traceId, Map<String, String> extraFields) {
        sendLogAsync(level, logger, message, traceId, extraFields, System.currentTimeMillis());
    }

    /**
     * 异步发送日志（带时间戳）
     */
    @Async
    public void sendLogAsync(String level, String logger, String message, String traceId, Map<String, String> extraFields, long timestamp) {
        if (!running.get() || slsClient == null) {
            return;
        }

        try {
            LogItem logItem = new LogItem();
            logItem.SetTime((int) (timestamp / 1000));

            // 基础字段
            logItem.PushBack("level", level);
            logItem.PushBack("logger", logger);
            logItem.PushBack("message", message);
            logItem.PushBack("timestamp", DateUtil.format(new Date(timestamp), "yyyy-MM-dd HH:mm:ss"));
            logItem.PushBack("hostname", getHostname());
            logItem.PushBack("application", getApplicationName());

            // 链路追踪ID
            logItem.PushBack("traceId", traceId);

            // 额外字段
            if (extraFields != null && !extraFields.isEmpty()) {
                extraFields.forEach(logItem::PushBack);
            }

            // 添加到队列，高并发场景下快速失败
            if (!logQueue.offer(logItem)) {
                // 队列满时静默丢弃，避免影响主业务性能
                // 在高并发场景下，保证主业务性能比日志完整性更重要
                return;
            }
        } catch (Exception e) {
            log.error("发送SLS日志失败", e);
        }
    }

    /**
     * 获取应用名称
     */
    private String getApplicationName() {
        return slsLogConfig.getTopic(); // 使用topic作为应用名称
    }

    /**
     * 批量发送日志
     */
    private void sendBatchLogs() {
        if (!running.get() || logQueue.isEmpty()) {
            return;
        }

        List<LogItem> logs = new ArrayList<>();
        int count = 0;
        int totalSize = 0;

        // 从队列中取出日志
        while (count < slsLogConfig.getBatchCountThreshold() && 
               totalSize < slsLogConfig.getBatchSizeThresholdInBytes()) {
            LogItem logItem = logQueue.poll();
            if (logItem == null) {
                break;
            }
            logs.add(logItem);
            count++;
            totalSize += estimateLogItemSize(logItem);
        }

        if (logs.isEmpty()) {
            return;
        }

        try {
            PutLogsRequest request = new PutLogsRequest(
                slsLogConfig.getProject(),
                slsLogConfig.getLogstore(),
                slsLogConfig.getTopic(),
                slsLogConfig.getSource(),
                logs
            );

            PutLogsResponse response = slsClient.PutLogs(request);
            if (slsLogConfig.isDebugMode()) {
                log.debug("SLS日志发送成功，批次大小: {}, RequestId: {}", 
                    logs.size(), response.GetRequestId());
            }
        } catch (Exception e) {
            log.error("SLS批量发送日志失败，批次大小: {}，高并发场景下丢弃以避免积压", logs.size(), e);
            // 高并发场景下不重新入队，避免队列积压导致内存问题
            // 在60万条/30秒的场景下，保证系统稳定性比日志完整性更重要
        }
    }

    /**
     * 估算日志项大小
     */
    private int estimateLogItemSize(LogItem logItem) {
        int size = 0;
        for (int i = 0; i < logItem.GetLogContents().size(); i++) {
            size += logItem.GetLogContents().get(i).GetKey().length();
            size += logItem.GetLogContents().get(i).GetValue().length();
        }
        return size + 50; // 加上一些元数据开销
    }

    /**
     * 获取主机名
     */
    private String getHostname() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown";
        }
    }



    /**
     * 发送业务日志
     */
    public void sendBusinessLog(String businessType, String operation, String result, 
                               String traceId, Map<String, String> businessData) {
        Map<String, String> extraFields = new HashMap<>();
        extraFields.put("businessType", businessType);
        extraFields.put("operation", operation);
        extraFields.put("result", result);
        if (businessData != null) {
            extraFields.putAll(businessData);
        }
        
        String message = String.format("业务操作: %s - %s, 结果: %s", businessType, operation, result);
        sendLogAsync("INFO", "BUSINESS", message, traceId, extraFields);
    }
}
