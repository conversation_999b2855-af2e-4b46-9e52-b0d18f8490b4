package net.armcloud.paascenter.commscenter.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.TransmissionDataDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.comms.AppCmdRecord;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.BatchUtils;
import net.armcloud.paascenter.common.utils.FeignUtils;
import net.armcloud.paascenter.common.utils.http.HttpClientUtils;
import net.armcloud.paascenter.commscenter.mapper.AppCmdRecordMapper;
import net.armcloud.paascenter.commscenter.mapper.CmdRecordMapper;
import net.armcloud.paascenter.commscenter.model.bo.CommsMessageBodyBO;
import net.armcloud.paascenter.commscenter.model.cache.ServerCache;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.config.PullModeConfigHolder;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.*;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.CommsCmdStatus.FAIL_COMMS_CMD_RECORD_STATUS;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;
import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.PAD_CMD_RUNNING_KEY_PREFIX;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@RefreshScope
@Slf4j
@Component
public class PadCmdManager implements ApplicationRunner {

    @Value("${image-cmd.timeout}")
    private Long upgradeImageCmdDefaultTimeout;

    private final RedisService redisService;
    private final CommscenterTaskManager commscenterTaskManager;
    private final CmdRecordMapper cmdRecordMapper;
    private final AppCmdRecordMapper appCmdRecordMapper;
    private final ApplicationContext applicationContext;
    private final PadMapper padMapper;
    /**
     * 默认超时时间 2038-01-01 00:00:00
     */
    private final Date defaultExpirationDate = new Date(2145888000000L);

    private final Long imageCmdDefaultTimeout = 14400000L;

    /**
     * 指令超时配置
     */
    private final Map<String, Long> commandTimeoutMap = new HashMap<>();

    /**
     * 任务类型关联指令
     */
    private final Map<Integer, String> taskTypeCommandMap = new HashMap<>();

    private final CommonPadTaskComponent commonPadTaskComponent;

    /**
     * 分发指令数据到pad
     */
    public List<CommsTransmissionResultVO> send(PadCMDForwardDTO request) {
        String command = request.getCommand().getCommand();

        if (command.equals(CommsCommandEnum.JOIN_VOLCANO_SHARE_ROOM.getCommand()) ||
                command.equals(CommsCommandEnum.JOIN_ARMCLOUD_SHARE_ROOM.getCommand()) ||
                command.equals(CommsCommandEnum.PUSH_VOLCANO_FLOW.getCommand())) {
            log.debug("start forwardData >>>> request: {}", JSON.toJSONString(request));
        }

        List<PadCMDForwardDTO.PadInfoDTO> padInfos = request.getPadInfos();
        List<CommsTransmissionResultVO> result = new ArrayList<>(padInfos.size());
        Map<String, String> cmdRunningPadCodeMap = new HashMap<>();
        Map<ServerCache, List<String>> serverCachePadCodeMap = new HashMap<>();
        PadConnectionManager padConnectionManager = applicationContext.getBean(PadConnectionManager.class);
        List<String> reqPadCodes = new ArrayList<>();
        padInfos.forEach(padInfo -> {
            reqPadCodes.add(padInfo.getPadCode());
            initializationRequestId(padInfo);
        });

        saveAppRefCmdRecord(command, padInfos, request.getOprBy());
        List<CmdRecord> cmdRecords = null;
        List<Pad> pads = padMapper.selectPadByPadCodes(reqPadCodes);
        if(CollUtil.isNotEmpty(pads)){
            TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCommsCommand(command);
            //只有gs任务区分任务类型
            Boolean isGsTask = taskTypeAndChannelEnum!=null && TaskChannelEnum.GAMESERVER.getCode().equals(taskTypeAndChannelEnum.getChannel());
            //拉任务 实例
            List<String> pullPadCodes = new ArrayList<>();
            //推任务 实例
            List<String> pushPadCodes = new ArrayList<>();
            for(Pad pad1 : pads){
                if((pad1.getTaskMode() != null && pad1.getTaskMode() == 1) || !isGsTask){
                    pullPadCodes.add(pad1.getPadCode());
                }else{
                    pushPadCodes.add(pad1.getPadCode());
                }
            }
            if(CollUtil.isNotEmpty(pullPadCodes)){
                cmdRecords = persistenceCommandRecordPullMode(request);
                TaskTypeConstants taskType = taskTypeAndChannelEnum!=null?TaskTypeConstants.fromCode(taskTypeAndChannelEnum.getTaskCode()):EXECUTE_COMMAND;
                commonPadTaskComponent.addPadCMDTask(pads.get(0).getCustomerId(),pullPadCodes, taskType, request);
            }
            if(CollUtil.isNotEmpty(pushPadCodes)){
                List<PadCMDForwardDTO.PadInfoDTO> pushPadInfos = new ArrayList<>();
                for(PadCMDForwardDTO.PadInfoDTO padInfoDTO : padInfos){
                    if(pushPadCodes.contains(padInfoDTO.getPadCode())){
                        pushPadInfos.add(padInfoDTO);
                        groupByServer(padInfoDTO, serverCachePadCodeMap, padConnectionManager, cmdRunningPadCodeMap);
                    }
                }
                // 指令日志入库
                cmdRecords = persistenceCommandRecord(serverCachePadCodeMap, request, cmdRunningPadCodeMap);
                // 筛选未找到服务器记录
                cmdRecords.stream()
                        .filter(commsCmdRecord -> Objects.equals(commsCmdRecord.getStatus(), FAIL_COMMS_CMD_RECORD_STATUS))
                        .forEach(commsCmdRecord -> result.add(CommsTransmissionResultVO.builderWithNotFoundServer(commsCmdRecord.getPadCode())));
                // 按服务器分组批量转发pad指令
                Map<String, PadCMDForwardDTO.PadInfoDTO> padCodeInfoMap = pushPadInfos.stream()
                        .collect(Collectors.toMap(PadCMDForwardDTO.PadInfoDTO::getPadCode, obj -> obj, (o1, o2) -> o1));
                serverCachePadCodeMap.forEach((server, padCodes) -> {
                    try {
                        // 分发指令
                        Map<String, Object> padCodeDataMap = new HashMap<>(padCodes.size());
                        padCodes.forEach(padCode -> padCodeDataMap.put(padCode, new CommsMessageBodyBO(command, padCodeInfoMap.get(padCode).getData())));
                        result.addAll(transmissionCommand(server, padCodeDataMap));
                        log.info("Transmission result: {}", JSON.toJSONString(result));
                    } catch (Exception e) {
                        padCodes.forEach(padCode -> result.add(CommsTransmissionResultVO.builderWithSendFail(padCode)));
                        log.info("Transmission error", e);
                    }
                });
            }
        }

        if(CollUtil.isNotEmpty(cmdRecords)){
            Map<String, CmdRecord> padCodeRefRecordMap = cmdRecords.stream()
                    .collect(Collectors.toMap(CmdRecord::getPadCode, obj -> obj, (o1, o2) -> o1));
            updateErrorInfo(result, padCodeRefRecordMap);
            setRequestId(result, padCodeRefRecordMap);
            commscenterTaskManager.updateTaskMsg(cmdRecords);
        }
        return result;
    }

    private void setRequestId(List<CommsTransmissionResultVO> result, Map<String, CmdRecord> padCodeRefRecordMap) {
        result.forEach(r -> {
            String padCode = r.getPadCode();
            CmdRecord cmdRecord = padCodeRefRecordMap.get(padCode);
            if (Objects.isNull(cmdRecord)) {
                return;
            }

            r.setRequestId(cmdRecord.getRequestId());
        });
    }

    private void saveAppRefCmdRecord(String command, List<PadCMDForwardDTO.PadInfoDTO> padInfos, String oprBy) {
        List<String> saveCommands = Arrays.asList(
                CommsCommandEnum.DOWNLOAD_FILE_APP_CMD.getCommand(),
                CommsCommandEnum.START_APP_CMD.getCommand(),
                CommsCommandEnum.STOP_APP_CMD.getCommand(),
                CommsCommandEnum.RESTART_APP_CMD.getCommand(),
                CommsCommandEnum.UNINSTALL_APP_CMD.getCommand()
        );

        if (!saveCommands.contains(command)) {
            return;
        }

        List<AppCmdRecord> appCmdRecords = new ArrayList<>(padInfos.size());
        padInfos.forEach(padInfoDTO -> {
            JSONObject jsonObject = (JSONObject) padInfoDTO.getData();
            String packageName = jsonObject.getString(PACKAGE_NAME);
            if (StringUtils.isBlank(packageName)) {
                return;
            }

            AppCmdRecord appCmdRecord = new AppCmdRecord();
            appCmdRecord.setRequestId(jsonObject.getString(REQUEST_ID));
            appCmdRecord.setPackageName(packageName);
            appCmdRecord.setPadCode(padInfoDTO.getPadCode());
            appCmdRecord.setCreateBy(oprBy);
            appCmdRecords.add(appCmdRecord);
        });

        BatchUtils.batchHandling(appCmdRecords, 100, records -> appCmdRecordMapper.batchInsert(null, records));
    }

    private void groupByServer(PadCMDForwardDTO.PadInfoDTO padInfo, Map<ServerCache, List<String>> serverCachePadCodeMap,
                               PadConnectionManager padConnectionManager, Map<String, String> cmdRunningPadCodeMap) {
        String padCode = padInfo.getPadCode();
        ServerCache serverCache = padConnectionManager.getServer(padCode);
        if (Objects.isNull(serverCache)) {
            return;
        }

        if (cmdRunningPadCodeMap.containsKey(padCode)) {
            return;
        }

        long serverId = serverCache.getId();
        ServerCache mapServer = serverCachePadCodeMap.keySet().stream()
                .filter(s -> s.getId() == serverId).findFirst()
                .orElse(null);
        if (Objects.nonNull(mapServer)) {
            List<String> inServerPadCodes = serverCachePadCodeMap.get(mapServer);
            inServerPadCodes.add(padCode);
            serverCachePadCodeMap.put(mapServer, inServerPadCodes);
            return;
        }

        serverCachePadCodeMap.put(serverCache, new ArrayList<>(Collections.singletonList(padCode)));
    }


    private void updateErrorInfo(List<CommsTransmissionResultVO> result, Map<String, CmdRecord> padCodeRefRecordMap) {
        List<CmdRecord> failRecords = new ArrayList<>();
        result.stream()
                .filter(r -> !r.getSendSuccess())
                .forEach(r -> {
                    String padCode = r.getPadCode();
                    CmdRecord cmdRecord = padCodeRefRecordMap.get(padCode);
                    if (Objects.isNull(cmdRecord)) {
                        return;
                    }

                    cmdRecord.setStatus(FAIL_COMMS_CMD_RECORD_STATUS);
                    if (Boolean.FALSE.equals(r.getFoundPadCode())) {
                        cmdRecord.setErrorMsg("系统服务无法连接");
                        return;
                    }

                    if (Boolean.FALSE.equals(r.getSendSuccess())) {
                        cmdRecord.setErrorMsg("发送失败");
                    }
                });

        if (CollectionUtils.isEmpty(failRecords)) {
            return;
        }

        BatchUtils.batchHandling(failRecords, 50, tempRecords -> cmdRecordMapper.batchUpdateError(null, tempRecords));
    }

    private void initializationRequestId(PadCMDForwardDTO.PadInfoDTO padInfo) {
        String padCode = padInfo.getPadCode();
        JSONObject dataJSONObject;
        if (ObjectUtil.isEmpty(padInfo.getData())) {
            dataJSONObject = new JSONObject();
        } else {
            dataJSONObject = JSONObject.from(padInfo.getData());
        }
        String requestId = padCode + System.currentTimeMillis();
        dataJSONObject.put(REQUEST_ID, requestId);
        padInfo.setData(dataJSONObject);
    }

    private List<CmdRecord> persistenceCommandRecord(Map<ServerCache, List<String>> serverCachePadCodeMap,
                                                     PadCMDForwardDTO request, Map<String, String> cmdRunningPadCodeMap) {
        log.info("persistenceCommandRecord serverCachePadCodeMap:{},request:{},cmdRunningPadCodeMap:{}",serverCachePadCodeMap,request,cmdRunningPadCodeMap);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = request.getPadInfos();
        List<CmdRecord> cmdRecords = new ArrayList<>(padInfos.size());
        String command = request.getCommand().getCommand();

        padInfos.parallelStream().forEach(padInfo -> {
            String padCode = padInfo.getPadCode();
            AtomicReference<ServerCache> serverCacheAtomicReference = new AtomicReference<>();
            serverCachePadCodeMap.forEach((server, padCodes) -> {
                if (padCodes.contains(padCode)) {
                    serverCacheAtomicReference.set(server);
                }
            });

            JSONObject padCmdObj = JSONObject.from(padInfo.getData());
            CmdRecord cmdRecord = new CmdRecord()
                    .setRequestId(padCmdObj.getString(REQUEST_ID))
                    .setTaskId(Optional.ofNullable(padCmdObj.getLong(TASK_ID)).orElse(0L))
                    .setSubTaskId(Optional.ofNullable(padCmdObj.getLong(SUB_TASK_ID)).orElse(0L))
                    .setSource(request.getSourceCode().getCode())
                    .setCommand(command)
                    .setPadCode(padCode)
                    .setTimeoutTime(getTimeOutDate(command))
                    .setCommandContent(JSON.toJSONString(padCmdObj));

            ServerCache serverCache = serverCacheAtomicReference.get();
            boolean notFoundServer = Objects.isNull(serverCache);
            cmdRecord.setCommsServerId(notFoundServer ? 0L : serverCache.getId());
            log.info("persistenceCommandRecord cmdRunningPadCodeMap:{},padCode:{},notFoundServer:{}",cmdRunningPadCodeMap,padCode,notFoundServer);

            if (cmdRunningPadCodeMap.containsKey(padCode)) {
                cmdRecord.setStatus(FAIL_COMMS_CMD_RECORD_STATUS);
                cmdRecord.setErrorMsg("running cmd");
                cmdRecords.add(cmdRecord);
                return;
            }

            if (notFoundServer) {
                cmdRecord.setStatus(FAIL_COMMS_CMD_RECORD_STATUS);
                cmdRecord.setErrorMsg("系统服务无法连接");
                cmdRecords.add(cmdRecord);
                return;
            }

            cmdRecord.setStatus(SUCCESS_COMMS_CMD_RECORD_STATUS);
            cmdRecords.add(cmdRecord);
        });

        BatchUtils.batchHandling(cmdRecords, 50, records -> cmdRecordMapper.batchInsert(null, records));
        return cmdRecords;
    }

    private List<CmdRecord> persistenceCommandRecordPullMode(PadCMDForwardDTO request) {
//        log.info("persistenceCommandRecord request:{}",request);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = request.getPadInfos();
        List<CmdRecord> cmdRecords = new ArrayList<>(padInfos.size());
        String command = request.getCommand().getCommand();

        padInfos.parallelStream().forEach(padInfo -> {
            String padCode = padInfo.getPadCode();

            JSONObject padCmdObj = JSONObject.from(padInfo.getData());
            CmdRecord cmdRecord = new CmdRecord()
                    .setRequestId(padCmdObj.getString(REQUEST_ID))
                    .setTaskId(Optional.ofNullable(padCmdObj.getLong(TASK_ID)).orElse(0L))
                    .setSubTaskId(Optional.ofNullable(padCmdObj.getLong(SUB_TASK_ID)).orElse(0L))
                    .setSource(request.getSourceCode().getCode())
                    .setCommand(command)
                    .setPadCode(padCode)
                    .setTimeoutTime(getTimeOutDate(command))
                    .setCommandContent(JSON.toJSONString(padCmdObj));
            cmdRecord.setStatus(SUCCESS_COMMS_CMD_RECORD_STATUS);
            cmdRecords.add(cmdRecord);
        });

        BatchUtils.batchHandling(cmdRecords, 50, records -> cmdRecordMapper.batchInsert(null, records));
        return cmdRecords;
    }

    private List<CommsTransmissionResultVO> transmissionCommand(ServerCache serverCache, Map<String, Object> padCodeDataMap) {
        int padSize = padCodeDataMap.keySet().size();

        List<TransmissionDataDTO> transmissionDataDTOS = new ArrayList<>(padSize);
        padCodeDataMap.forEach((padCode, data) -> {
            TransmissionDataDTO transmissionDataDTO = new TransmissionDataDTO();
            transmissionDataDTO.setPadCode(padCode);
            transmissionDataDTO.setData(data);
            transmissionDataDTOS.add(transmissionDataDTO);
        });

        String domain = "http://" + serverCache.getPublicIp() + ":" + serverCache.getPublicInterfacePort();
        String url = domain + "/comms/internal/pad/transmissionCommand";
        log.info("transmissionCommand url:{}",url);
        String data = JSON.toJSONString(transmissionDataDTOS);
        log.info("transmissionCommand data:{}",data);
        try {
            String resultStr = HttpClientUtils.doPost(url, data);
            Result<List<CommsTransmissionResultVO>> result = JSON.parseObject(resultStr, new TypeReference<Result<List<CommsTransmissionResultVO>>>() {
            });
            return FeignUtils.getContent(result);
        } catch (Exception e) {
            log.error("transmissionCommand error>>>>>url:{},data:{}", url, data, e);
            List<CommsTransmissionResultVO> resultVos = new ArrayList<>(padSize);
            padCodeDataMap.keySet().forEach(padCode -> {
                CommsTransmissionResultVO commsTransmissionResultVO = new CommsTransmissionResultVO();
                commsTransmissionResultVO.setPadCode(padCode);
                commsTransmissionResultVO.setSendSuccess(false);
                commsTransmissionResultVO.setFoundPadCode(false);
                resultVos.add(commsTransmissionResultVO);
            });

            return resultVos;
        }
    }

    public Date getTimeOutDate(String command) {
        Long timeoutMilliseconds = commandTimeoutMap.get(command);
        if (Objects.nonNull(timeoutMilliseconds)) {
            if (CommsCommandEnum.UPGRADE_IMAGE.getCommand().equals(command)) {
                Long timeout = isNotEmpty(upgradeImageCmdDefaultTimeout) ? upgradeImageCmdDefaultTimeout : timeoutMilliseconds;
                return new Date(System.currentTimeMillis() + timeout);
            }
            return new Date(System.currentTimeMillis() + timeoutMilliseconds);
        }

        return defaultExpirationDate;
    }



    public void deleteCmdRunningLimit(String padCode) {
        String key = PAD_CMD_RUNNING_KEY_PREFIX + padCode;
        redisService.deleteObject(key);
    }

    @Override
    public void run(ApplicationArguments args) {
//        initTaskTypeCommandMap();
//        List<TaskTimeoutConfig> taskTimeoutConfigs = taskManager.listTimeoutConfig();
//        if (CollectionUtils.isEmpty(taskTimeoutConfigs)) {
//            return;
//        }
//
//        taskTimeoutConfigs.forEach(taskTimeoutConfig -> {
//            String command = taskTypeCommandMap.get(taskTimeoutConfig.getTaskType());
//            if (StringUtils.isBlank(command)) {
//                return;
//            }
//
//            commandTimeoutMap.put(command, taskTimeoutConfig.getTimeoutMillisecond());
//        });

    }

    private void initTaskTypeCommandMap() {
        taskTypeCommandMap.put(EXECUTE_COMMAND.getType(), CommsCommandEnum.ADB_CMD.getCommand());
        taskTypeCommandMap.put(TaskTypeConstants.RESTART.getType(), CommsCommandEnum.RESTART.getCommand());
        taskTypeCommandMap.put(TaskTypeConstants.RESET.getType(), CommsCommandEnum.RESET.getCommand());
        taskTypeCommandMap.put(LIST_INSTALLED_APP.getType(), CommsCommandEnum.LIST_INSTALL_APP.getCommand());
        taskTypeCommandMap.put(UPDATE_PAD_PROPERTIES.getType(), CommsCommandEnum.UPDATE_PROPERTIES.getCommand());
        taskTypeCommandMap.put(UPDATE_PAD_PROPERTIES.getType(), CommsCommandEnum.UPDATE_PROPERTIES.getCommand());
        taskTypeCommandMap.put(TaskTypeConstants.UPGRADE_IMAGE.getType(), CommsCommandEnum.UPGRADE_IMAGE.getCommand());
        taskTypeCommandMap.put(APP_BLACK_LIST.getType(), CommsCommandEnum.UPDATE_BLACK_LIST.getCommand());
        taskTypeCommandMap.put(APP_WHITE_LIST.getType(), CommsCommandEnum.UPDATE_WHITE_LIST.getCommand());
    }

    public PadCmdManager(RedisService redisService, CommscenterTaskManager commscenterTaskManager, CmdRecordMapper cmdRecordMapper, ApplicationContext applicationContext, AppCmdRecordMapper appCmdRecordMapper,
                         PadMapper padMapper,CommonPadTaskComponent commonPadTaskComponent) {
        this.redisService = redisService;
        this.commscenterTaskManager = commscenterTaskManager;
        this.cmdRecordMapper = cmdRecordMapper;
        this.applicationContext = applicationContext;
        this.appCmdRecordMapper = appCmdRecordMapper;
        this.padMapper = padMapper;
        this.commonPadTaskComponent = commonPadTaskComponent;
    }
}
