package net.armcloud.paascenter.openapi.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadBackupTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskInfoVO;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.StorageCapacityDetailVO;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.vo.api.*;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.annotation.CustomerPadAuthValid;
import net.armcloud.paascenter.openapi.constants.AdbConstants;
import net.armcloud.paascenter.openapi.manager.FileManager;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageNetWorkOffDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResPadBackupDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResPadDeleteDTO;
import net.armcloud.paascenter.openapi.model.vo.*;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageCreateVO;
import net.armcloud.paascenter.openapi.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static net.armcloud.paascenter.openapi.annotation.CustomerPadAuthValid.FieldName.PAD_CODE;
import static net.armcloud.paascenter.openapi.annotation.CustomerPadAuthValid.FieldName.PAD_CODES;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@RestController
@RequestMapping("/openapi/open/pad")
@Api(tags = "实例")
@Slf4j
public class PadController {
    private final IPadService padService;
    private final FileManager fileManager;
    private final IPadAppService padAppService;
    private final IPadConnectService padConnectService;
    private final IPadBackupService padBackupService;
    private final IKeepAliveAppPadService keepAliveAppPadService;

    public PadController(IPadService padService, IPadAppService padAppService, FileManager fileManager,
                         IPadConnectService padConnectService, IPadBackupService padBackupService,IKeepAliveAppPadService keepAliveAppPadService) {
        this.padService = padService;
        this.padAppService = padAppService;
        this.fileManager = fileManager;
        this.padConnectService = padConnectService;
        this.padBackupService = padBackupService;
        this.keepAliveAppPadService = keepAliveAppPadService;
    }

    @CustomerPadAuthValid()
    @PostMapping("screenshot")
    @ApiOperation(value = "截图")
    public Result<List<GeneratePadTaskVO>> screenshotLocal(@Valid @RequestBody ScreenshotLocalDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.screenshotLocal(param));
    }

    @PostMapping("generatePreview")
    @ApiOperation(value = "生成预览图")
    @CustomerPadAuthValid
    public Result<List<GeneratePreviewVO>> generatePreview(@Valid @RequestBody ScreenshotLocalDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.generatePreview(param));
    }

    @PostMapping("getLongGenerateUrl")
    @ApiOperation(value = "获取长效预览图URL")
    public Result<List<LongPreviewVO>> getLongGenerateUrl(@Valid @RequestBody LongPreviewDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        log.info("获取长效预览图URL参数 {}", JSONUtil.toJsonStr(param));
        return Result.ok(padService.getLongGenerateUrl(param));
    }

    @PostMapping("asyncCmd")
    @ApiOperation(value = "执行异步ADB命令")
    @CustomerPadAuthValid
    public Result<List<AsyncCmdVO>> asyncCmd(@Valid @RequestBody ExecuteADBDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.asyncCmd(param));
    }



    @PostMapping("switchRoot")
    @ApiOperation(value = "开关root权限")
    @CustomerPadAuthValid
    public Result<List<AsyncCmdVO>> switchRoot(@Valid @RequestBody SwitchRootDTO param) {
        ExecuteADBDTO adbDTO = new ExecuteADBDTO();
        adbDTO.setPadCodes(param.getPadCodes());
        adbDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        //开启全局root
        if(param.getGlobalRoot()){
            adbDTO.setScriptContent(AdbConstants.replaceAdbStr(AdbConstants.SWITCH_GLOBAL_ROOT,String.valueOf(param.getRootStatus())));
            return Result.ok(padService.asyncCmd(adbDTO));
        }
        //非全局但是没传包名
        if(StringUtils.isEmpty(param.getPackageName())){
            return Result.fail(SWITCH_ROOT_PACKAGE_NOT_NULL);
        }
        String script = AdbConstants.SWITCH_SIGNE_ROOT
                .replace("{PACKAGE_NAME}", param.getPackageName())
                .replace("{ROOT_STATUS}", String.valueOf(param.getRootStatus()));
        adbDTO.setScriptContent(script);
        log.info("PadController_switchRoot script:{}",script);
        return Result.ok(padService.asyncCmd(adbDTO));
    }

    @PostMapping("uploadFile")
    @ApiOperation(value = "文件上传实例")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> uploadFile(@Valid @RequestBody PadDownloadFileDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        if (isNotEmpty(param.getTargetDirectory()) && !param.getTargetDirectory().startsWith("/")) {
            return Result.fail(THE_PATH_IS_INCORRECT);
        }
        return Result.ok(padService.uploadFile(param));
    }

    @PostMapping("/v2/uploadFile")
    @ApiOperation(value = "文件上传实例")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> uploadFileV2(@Valid @RequestBody PadDownloadFileV2DTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        if (isNotEmpty(param.getCustomizeFilePath()) && !param.getCustomizeFilePath().startsWith("/")) {
            return Result.fail(THE_PATH_IS_INCORRECT);
        }
        List<GeneratePadTaskVO> generatePadTaskVOList = padService.uploadFileV2(param);
        log.info("uploadFileV2返回参数:{}", JSON.toJSONString(generatePadTaskVOList));
        return Result.ok(generatePadTaskVOList);
    }

    @PostMapping("/v3/uploadFile")
    @ApiOperation(value = "文件上传实例")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> uploadFileV3(@Valid @RequestBody PadDownloadFileV3DTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        if (isNotEmpty(param.getCustomizeFilePath()) && !param.getCustomizeFilePath().startsWith("/")) {
            return Result.fail(THE_PATH_IS_INCORRECT);
        }
        List<GeneratePadTaskVO> generatePadTaskVOList = padService.uploadFileV3(param);
        log.info("uploadFileV3返回参数:{}", JSON.toJSONString(generatePadTaskVOList));
        return Result.ok(generatePadTaskVOList);
    }

    @PostMapping("installApp")
    @ApiOperation(value = "应用安装")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> installApp(@Valid @RequestBody InstallApp params) {
        log.info("应用安装请求参数 {}", JSONUtil.toJsonStr(params));
        List<GeneratePadTaskVO> result = new ArrayList<>();
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        params.getApps().forEach(param -> {
            param.setCustomerId(customerId);
            //卸载应用就不要校验文件是否存在了 只需要包名
            // fileManager.getCustomerAppFile(customerId, param.getAppId(), param.getAppName(), param.getPkgName());
            List<GeneratePadTaskVO> padTaskVOS = padAppService.installApp(param);
            result.addAll(padTaskVOS);
        });
        return Result.ok(result);
    }

    @PostMapping("uninstallApp")
    @ApiOperation(value = "卸载应用")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> uninstallApp(@Valid @RequestBody UnInstallApp params) {
        List<GeneratePadTaskVO> result = new ArrayList<>();
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        //兼容 appid和包名 有一个必填
        for(PadUninstallAppFileDTO padUninstallAppFileDTO : params.getApps()){
            if(StrUtil.isEmpty(padUninstallAppFileDTO.getAppId()) && StrUtil.isEmpty(padUninstallAppFileDTO.getPkgName())){
                return Result.fail("应用id和包名不能同时为空");
            }
        }
        params.getApps().forEach(param -> {
            param.setCustomerId(customerId);
            //fileManager.getCustomerAppFile(customerId, param.getAppId(), param.getAppName(), param.getPkgName());
            List<GeneratePadTaskVO> padTaskVOS = padAppService.uninstallApp(param);
            result.addAll(padTaskVOS);
        });
        return Result.ok(result);
    }

    @PostMapping("startApp")
    @ApiOperation(value = "启动应用")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> startApp(@Valid @RequestBody PadAppPackageNameDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padAppService.startApp(param));
    }

    @PostMapping("stopApp")
    @ApiOperation(value = "停止应用")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> stopApp(@Valid @RequestBody PadAppPackageNameDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padAppService.stopApp(param));
    }

    @PostMapping("restartApp")
    @ApiOperation(value = "重启应用")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> restartApp(@Valid @RequestBody PadAppPackageNameDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padAppService.restartApp(param));
    }

    @PostMapping("restart")
    @ApiOperation(value = "重启实例")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> restart(@RequestBody RestartDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.restartService(param));
    }


    /**
     * 此方法用来鉴权，不做逻辑处理
     * @param restartTestDTO
     * @return
     */
    @PostMapping("restart/test")
    public Result<?> restartTest(@RequestBody RestartTestDTO restartTestDTO) {
        restartTestDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        log.info("restartTest方法进来了:{}",JSON.toJSONString(restartTestDTO));
        return Result.ok();
    }

    @PostMapping("reset")
    @ApiOperation(value = "重置实例")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> reset(@Valid @RequestBody ResetDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.restService(param));
    }

    @PostMapping("resetGAID")
    @ApiOperation(value = "重置GAID")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> resetGAID(@Valid @RequestBody ResetGaidDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.resetGAID(param));
    }

    @PostMapping("syncCmd")
    @ApiOperation(value = "执行同步命令")
    @CustomerPadAuthValid(fieldName = PAD_CODE)
    public Result<List<SyncCmdVO>> syncCmd(@Valid @RequestBody SyncCmdDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.syncCmd(param, SourceTargetEnum.PAAS));
    }

    @PostMapping("openOnlineAdb")
    @ApiOperation(value = "开启关闭ADB")
    @CustomerPadAuthValid(fieldName = PAD_CODES)
    public Result<List<SyncCmdVO>> openOnlineAdb(@Valid @RequestBody OpenOnlineDTO param) {
       List<SyncCmdVO> resultList = Lists.newArrayList();
        param.getPadCodes().forEach(padcode ->{
            try{
                SyncCmdDTO dto = new SyncCmdDTO();
                dto.setScriptContent("setprop persist.sys.cloud.madb_enable "+param.getOpenStatus());
                dto.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
                dto.setPadCode(padcode);
                List<SyncCmdVO> syncCmdVOS = padService.syncCmd(dto, SourceTargetEnum.PAAS);
                padService.updatePadAdbOpenStatus(Lists.newArrayList(padcode),param.getOpenStatus());
                resultList.addAll(syncCmdVOS);
            }catch (BasicException ex){
                log.warn("PadController.openOnlineAdb process error:{}",ex.getMessage());
            }catch (Exception ex){
                log.error("PadController.openOnlineAdb process error:{}",ex.getMessage(),ex);
            }

        });
        return Result.ok(resultList);
    }


    @PostMapping("openOnlineAdb/getConnect")
    @ApiOperation(value = "获取ADB连接信息(如果ADB未开启,会发起一个开启adb任务)")
    @CustomerPadAuthValid(fieldName = PAD_CODE)
    public Result<AdbGetConnectVo> openOnlineAdbGetConnect(@Valid @RequestBody AdbConnectDTO param) {

        Pad pad = padService.getPadByPadCode(param.getPadCode());
        //未开启adb命令,直接发起一个开启adb任务
        if(Objects.equals(pad.getAdbOpenStatus(),0)){
            ExecuteADBDTO cmdDTO = new ExecuteADBDTO();
            cmdDTO.setPadCodes(Lists.newArrayList(param.getPadCode()));
            //开启ADB命令,返回任务ID
            cmdDTO.setScriptContent("setprop persist.sys.cloud.madb_enable "+1);
            cmdDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
            //只提交了一个任务,直接获取第0个元素,如果下游抛出异常,此处抛出异常,都是属于接口处理失败(任务失败) 此时返回的值只有任务ID信息,不会有连接信息,调用者需要通过判断taskId去判断
            Result<AdbGetConnectVo> ok = Result.ok(AdbGetConnectVo.buildByAsyncCmdVO(padService.asyncCmd(cmdDTO)));
            padService.updatePadAdbOpenStatus(Lists.newArrayList(param.getPadCode()),1);
            return ok;
        };
        //如果实例已经开启了adb,直接走原有逻辑获取adb返回
        PadAdbDTO dto = new PadAdbDTO();
        dto.setPadCode(param.getPadCode());
        dto.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        dto.setEnable(true);
        return Result.ok(AdbGetConnectVo.buildByPadAdbVO(padConnectService.padAdbConnect(dto)));
    }

    @PostMapping("listApp")
    @ApiOperation(value = "查询已安装的应用列表")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> listApp(@Valid @RequestBody PadCodesDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.listApp(param));
    }

    @PostMapping("padProperties")
    @ApiOperation(value = "查询实例属性")
    @CustomerPadAuthValid(fieldName = PAD_CODE)
    public Result<PadPropertiesVO> padProperties(@Valid @RequestBody PadPropertiesDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.padPropertiesService(param));
    }



    @PostMapping("batchPadProperties")
    @ApiOperation(value = "批量查询实例属性")
    @CustomerPadAuthValid(fieldName = PAD_CODES)
    public Result<List<PadPropertiesVO>> batchPadProperties(@Valid @RequestBody BatchPadPropertiesDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.batchPadProperties(param));
    }

    @PostMapping("updatePadProperties")
    @ApiOperation(value = "修改实例属性")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> updatePadProperties(@Valid @RequestBody UpdatePadPropertiesDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.updatePadPropertiesService(param));
    }

    @PostMapping("updatePadAndroidProp")
    @ApiOperation(value = "修改实例安卓改机属性")
    @CustomerPadAuthValid(fieldName = PAD_CODE)
    public Result<GeneratePadTaskVO> updatePadAndroidProp(@Valid @RequestBody UpdatePadAndroidPropDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.updatePadAndroidProp(param));
    }


    @PostMapping("setWifiList")
    @ApiOperation(value = "修改实例WIFI列表")
    @CustomerPadAuthValid(fieldName = PAD_CODES)
    public Result<List<GeneratePadTaskVO>> setWifiList(@Valid @RequestBody SetWifiListDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.setWifiList(param));
    }

    @PostMapping("replacePadAndroidPropByCountry")
    @ApiOperation(value = "根据国家随机生成SIM卡安卓属性并设置到实例上")
    @CustomerPadAuthValid(fieldName = PAD_CODE)
    public Result<GeneratePadTaskVO> replacePadAndroidPropByCountry (@Valid @RequestBody ReplacePadAndroidPropDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.replacePadAndroidPropByCountry(param));
    }

    @PostMapping("newPads")
    @ApiOperation(value = "批量一键新机")
    public Result<List<GeneratePadTaskVO>> newPads(@Valid @RequestBody NewPadsDTO param) {
        param.validateFields();
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.newPads(param));
    }

    @PostMapping("triggeringBlacklist")
    @ApiOperation(value = "触发黑名单列表")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> triggeringBlacklist(@Valid @RequestBody TriggeringBlackDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.triggeringBlackListService(param));
    }

    @PostMapping("padDetails")
    @ApiOperation(value = "查询实例详情")
    public Result<Page<PadDetailsVO>> padDetails(@Valid @RequestBody PadDetailsDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.padDetailsService(param));
    }



    @PostMapping("infos")
    @ApiOperation(value = "查询实例列表详情")
    public Result<Page<PadListVO>> infos(@Valid @RequestBody PadListDTO param) {
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        param.setCustomerId(customerId);
        log.info("查询实例列表详情, param:{}, customerId:{}",JSON.toJSONString(param), customerId);
        return Result.ok(padService.padListService(param));
    }

    @PostMapping("getIdlePad")
    @ApiOperation(value = "查询实例列表详情")
    public Result<List<PadIdleListVO>> getIdlePad() {
        return Result.ok(padService.padIdleListVOService());
    }

    @PostMapping("test")
    @ApiOperation(value = "查询实例列表详情")
    public Result<?> test() {
        log.info("------------test");
        PadPropertiesDTO param = new PadPropertiesDTO();
        param.setCustomerId(1L);
        param.setPadCode("AC22030022711");
        return Result.ok(padService.padPropertiesService(param));
    }

    @CustomerPadAuthValid(skipVerifyingTheOff = false)
    @PostMapping("modelInfo")
    @ApiOperation(value = "批量获取实例机型信息")
    public Result<List<PadModelInfoVO>> modelInfo(@Valid @RequestBody PadModelInfoDTO param) {
        return Result.ok(padService.PadModelInfo(param));
    }

    @PostMapping("upgradeImage")
    @ApiOperation(value = "升级镜像")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskInfoVO>> upgradeImage(@Valid @RequestBody UpgradeImageDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.upgradeImageService(param,SourceTargetEnum.PAAS));
    }

    @PostMapping("listInstalledApp")
    @ApiOperation(value = "实时查询已安装的应用列表")
    @CustomerPadAuthValid
    public Result<List<PadInstalledAppVO>> listInstalledApp(@Valid @RequestBody PadCodesDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padAppService.listInstalledApp(param));
    }

    @CustomerPadAuthValid()
    @PostMapping("setSpeed")
    @ApiOperation(value = "实例限速")
    public Result<List<GeneratePadTaskVO>> limitBandwidth(@Valid @RequestBody LimitBandwidthDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.limitBandwidthService(param));
    }

    @CustomerPadAuthValid()
    @PostMapping("updateTimeZone")
    @ApiOperation(value = "修改时区")
    public Result<List<GeneratePadTaskVO>> updateTimeZone(@Valid @RequestBody UpdatePadTimeZoneDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.updateTimeZone(param));
    }

    @CustomerPadAuthValid()
    @PostMapping("updateLanguage")
    @ApiOperation(value = "修改语言")
    public Result<List<GeneratePadTaskVO>> updateLanguage(@Valid @RequestBody UpdatePadLanguageDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.updateLanguage(param));
    }

    @CustomerPadAuthValid()
    @PostMapping("updateSIM")
    @ApiOperation(value = "修改SIM卡信息")
    public Result<List<GeneratePadTaskVO>> updateSIM(@Valid @RequestBody UpdatePadSIMDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.updateSIM(param));
    }

    @CustomerPadAuthValid
    @PostMapping("gpsInjectInfo")
    @ApiOperation(value = "设置经纬度")
    public Result startApp(@Valid @RequestBody GpsInjectInfoDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.gpsInjectInfo(param));
    }


    @CustomerPadAuthValid(fieldName = CustomerPadAuthValid.FieldName.PAD_CODE)
    @PostMapping("adb")
    @ApiOperation(value = "获取adb连接信息")
    public Result<PadAdbVO> adb(@Valid @RequestBody PadAdbDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padConnectService.padAdbConnect(param));
    }

    @PostMapping("getStreamType")
    public Result<List<GetStreamTypeVO>> getStreamType(@Valid @RequestBody PadCodesDTO param) {
        return Result.ok(padService.getStreamType(param));
    }

    /**
     * 一键替换新机
     *
     * @param param
     * @return
     */
    @PostMapping("/replacePad")
    @CustomerPadAuthValid(fieldName = CustomerPadAuthValid.FieldName.PAD_CODES)
    public Result<List<GeneratePadTaskVO>> replacePad(@Valid @RequestBody ReplacePadTaskDTO param) {
        try{
            param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
            return Result.ok(padService.replacePad(param, SourceTargetEnum.PAAS));
        } catch (BasicException ex){
            log.warn("PadController.replacePad result error:{}",ex.getMessage());
            return Result.fail(ex.getMessage());
        }catch (Exception e){
            log.error("PadController.replacePad result error:{}",e.getMessage(),e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 实例数据备份
     */
    @PostMapping("/data/backup")
    @CustomerPadAuthValid
    public Result<List<GeneratePadBackupTaskVO>> singleBackup(@Valid @RequestBody PadBackupDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padBackupService.backup(param));
    }

    /**
     * 开启关闭ADB
     *
     * @param param
     * @return
     */
    @PostMapping("/open_online_adb")
    public Result<List<GeneratePadTaskInfoVO>> openOnlineAdb(@Valid @RequestBody OpenOnlineAdbPadTaskDTO param) {
        try{
            param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
            return Result.ok(padService.openOnlineAdb(param, SourceTargetEnum.PAAS));
        } catch (Exception e){
            return Result.fail(e.getMessage());
        }

    }

    @PostMapping("virtualRealSwitch")
    @ApiOperation(value = "升级真机镜像")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskInfoVO>> virtualRealSwitchUpgradeImage(@Valid @RequestBody VirtualRealSwitchUpgradeImageDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        if(!param.checkParam()){
            return Result.fail(PARAM_REQUEST_ILLEGALITY);
        }
        return Result.ok(padService.virtualRealSwitchUpgradeImageService(param));
    }

    @PostMapping("replaceRealAdiTemplate")
    @ApiOperation(value = "升级真机adi模板")
    @CustomerPadAuthValid(fieldName = CustomerPadAuthValid.FieldName.PAD_CODES)
    public Result<List<GeneratePadTaskInfoVO>> replaceRealAdbTemplate (@Valid @RequestBody ReplaceRealAdbTemplateDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.replaceRealAdbTemplate(param,SourceTargetEnum.PAAS));
    }

    /**
     * 修改实例属性
     * @param param
     * @return
     */
    @PostMapping("/modifyPadProperties")
    public Result<GeneratePadTaskVO> modifyPadProperties(@Valid @RequestBody ModifyPadInformationDTO param){
        try {
            param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
            return Result.ok(padService.modifyPadProperties(param, SourceTargetEnum.PAAS));
        }catch (BasicException ex){
            log.warn("modifyPadProperties result error:{}",ex.getMessage());
            return Result.fail(ex.getMessage());
        }catch (Exception ex){
            log.error("modifyPadProperties result error",ex);
            return Result.fail(ex.getMessage());
        }
    }

    /**
     * 实例数据恢复
     */
    @PostMapping("/data/restore")
    @CustomerPadAuthValid(fieldName = PAD_CODES)
    public Result<List<GeneratePadTaskVO>> restore(@Valid @RequestBody PadRestoreDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padBackupService.restore(param));
    }


    /**
     * 实例数据删除
     */
    @PostMapping("/data/del")
    public Result<List<DataDelDTO>> dataDel(@Valid @RequestBody DataDelMasterDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        if (CollectionUtils.isEmpty(param.getDataDelDTOS()) || param.getDataDelDTOS().size() > 200){
            return Result.fail(DATA_DEL_NOT_EXIST);
        }

        //保存局部变量值
        int size = param.getDataDelDTOS().size();

        List<DataDelDTO> dataDelDTOList = padBackupService.dataDel(param);
        //如果没有失败的，则直接返回成功
        if (CollectionUtils.isEmpty(dataDelDTOList)){
            return Result.ok();
        }

        if (dataDelDTOList.size() == size) {
            return Result.fail(dataDelDTOList,"删除失败");
        }

        //存在失败的，则返回失败的
        return Result.ok(dataDelDTOList,"部分成功,存在无效实例编码与数据备份名称");
    }

    @PostMapping("triggeringWhitelist")
    @ApiOperation(value = "触发白名单列表")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> triggeringWhitelist(@Valid @RequestBody TriggeringBlackDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.triggeringWhiteListService(param));
    }


    /**
     * 查询那些实例存在当前指令任务，待执行或执行中
     * @param param
     * @return
     */
    @PostMapping("/exist/instruction/padCode")
    public Result<List<String>> existInstructionPadCode(@RequestBody ExistInstructionPadCodeDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.existInstructionPadCode(param));
    }

    /**
     * 设置保活应用
     * @param param
     * @return
     */
    @PostMapping("/setKeepAliveApp")
    @ApiOperation(value = "设置保活应用")
    public Result<List<GeneratePadTaskVO>> setKeepAliveApp(@Valid @RequestBody SetKeepAliveAppSaveDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        keepAliveAppPadService.setKeepAliveApp(param);
        return Result.ok();
    }

    /**
     * 更新通讯录
     * @param param
     * @return
     */
    @CustomerPadAuthValid
    @PostMapping("updateContacts")
    public Result<List<GeneratePadTaskVO>> updateContacts(@Valid @RequestBody UpdateContactsDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.updateContacts(param));
    }


    /**
     * 创建网存实例
     * @param param
     * @return
     */
    @PostMapping("/net/storage/res/create")
    Result<List<NetStorageCreateVO>> virtualizeNetStorageRes(@Valid @RequestBody NetWorkVirtualizeDTO param){
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.virtualizeNetStorageRes(param, SourceTargetEnum.PAAS));
    }

    /**
     * 网存实例开机
     * @param param
     * @return
     */
    @PostMapping("/net/storage/on")
    @CustomerPadAuthValid(fieldName = PAD_CODES,skipVerifyingTheOff = false)
    Result<List<GeneratePadTaskVO>> netStorageResBootOn(@Valid @RequestBody  NetWorkOnDTO param){
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.netStorageResBootOn(param, SourceTargetEnum.PAAS));
    }

    /**
     * 指定网存id的网存实例开机
     * @param param
     * @return
     */
    @PostMapping("/net/storage/specifiedCode/on")
    @CustomerPadAuthValid(fieldName = PAD_CODE,skipVerifyingTheOff = false)
    Result<GeneratePadTaskVO> netStorageResSpecifiedCodeBootOn(@Valid @RequestBody NetStorageNetWorkOffDTO param){
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.netStorageResSpecifiedCodeBootOn(param, SourceTargetEnum.PAAS));
    }

    /**
     * 网存实例关机
     * @param param
     * @return
     */
    @CustomerPadAuthValid(fieldName = PAD_CODES)
    @PostMapping("/net/storage/off")
    public Result<List<GeneratePadTaskVO>> netStorageResBootOff(@Valid @RequestBody NetWorkOffDTO param){
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

        return Result.ok(padService.netStorageResBootOff(param, SourceTargetEnum.PAAS));
    }

    /**
     * 网存资源回收
     * @param param
     * @return
     */
    @RequestMapping(value = "net/storage/delete", method = RequestMethod.POST)
    @ApiOperation(value = "实例删除(回收网存)", httpMethod = "POST", notes = "实例删除(回收网存)")
    @CustomerPadAuthValid(fieldName = PAD_CODES,skipVerifyingTheOff = false)
    public Result<List<GeneratePadTaskVO>> netStorageResDelete(@Valid @RequestBody NetWorkDeleteDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

        return Result.ok(padService.netStorageResDelete(param, SourceTargetEnum.PAAS));
    }

    /**
     * 网存实例更配(下次开机生效)
     * @param param
     * @return
     */
    @RequestMapping(value = "net/storage/update", method = RequestMethod.POST)
    @ApiOperation(value = "实例更配()", httpMethod = "POST", notes = "实例更配")
    @CustomerPadAuthValid(fieldName = PAD_CODES,skipVerifyingTheOff = false)
    public Result<String> netStorageResMoreCompatible(@Valid @RequestBody NetStorageResMoreCompatiblePaasDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        padService.netStorageResMoreCompatible(param,SourceTargetEnum.PAAS);
        return Result.ok();
    }



    @PostMapping(value = "net/detail/storageCapacity/available")
    @ApiOperation(value = "可用网存资源大小", httpMethod = "POST", notes = "可用网存资源大小")
    public Result<List<StorageCapacityDetailVO>> getDetailStorageCapacityAvailable(@RequestBody NetStorageResDetailDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.getDetailStorageCapacityAvailableList(param));
    }

    @PostMapping(value = "netPad/group/deviceLevel")
    @ApiOperation(value = "获取网存实例使用详情", httpMethod = "POST", notes = "获取网存实例使用详情")
    public Result<List<NetPadDeviceVO>> groupNetPadByDeviceLevel(@RequestBody NetStorageResDetailDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
         return Result.ok(padService.groupNetPadByDeviceLevel(param));
    }


    /**
     *网存存储备份
     * @param param
     * @return
     */
    //注解拦截未实现
//    @CustomerPadAuthValid(fieldName = RES_UNIT_CODE)
    @PostMapping("/net/storage/pad/backup")
    @CustomerPadAuthValid(fieldName = PAD_CODES,skipVerifyingTheOff = false)
    public Result<List<GeneratePadTaskVO>> netStoragePadBackup(@Valid @RequestBody NetStorageResPadBackupDTO param){
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.netStoragePadBackup(param, SourceTargetEnum.PAAS));
    }


    /**
     *网存存储删除
     * @param param
     * @return
     */
    //注解拦截未实现
//    @CustomerPadAuthValid(fieldName = RES_UNIT_CODE)
    @PostMapping("/net/storage/pad/delete")
    public Result<List<GeneratePadTaskVO>> netStoragePadDelete(@Valid @RequestBody NetStorageResPadDeleteDTO param){
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.netStoragePadDelete(param, SourceTargetEnum.PAAS));
    }

    @CustomerPadAuthValid()
    @PostMapping("simulateTouch")
    @ApiOperation(value = "模拟触控")
    public Result<List<GeneratePadTaskVO>> simulateTouch(@Valid @RequestBody SimulateTouchDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.simulateTouch(param));
    }

    @CustomerPadAuthValid()
    @PostMapping("inputText")
    @ApiOperation(value = "云机文本信息输入")
    public Result<List<GeneratePadTaskVO>> inputText(@Valid @RequestBody InputTextDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.inputText(param));
    }

    @CustomerPadAuthValid()
    @PostMapping("/addPhoneRecord")
    @ApiOperation(value = "导入通话记录")
    public Result<List<GeneratePadTaskVO>> addPhoneRecord(@Valid @RequestBody CallRecordsDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.addPhoneRecord(param));
    }

    @CustomerPadAuthValid()
    @PostMapping("/injectAudioToMic")
    @ApiOperation(value = "注入音频到实例麦克风")
    public Result<List<GeneratePadTaskVO>> injectAudioToMic(@Valid @RequestBody AudioToMicDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padService.injectAudioToMic(param));
    }

}
