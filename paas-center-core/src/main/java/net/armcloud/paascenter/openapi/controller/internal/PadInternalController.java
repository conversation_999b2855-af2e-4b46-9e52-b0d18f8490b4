package net.armcloud.paascenter.openapi.controller.internal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.facade.PadInternalFacade;
import net.armcloud.paascenter.common.client.internal.vo.*;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.vo.api.AsyncCmdVO;
import net.armcloud.paascenter.common.model.vo.api.PadAdbVO;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;
import net.armcloud.paascenter.common.model.vo.api.SyncCmdVO;
import net.armcloud.paascenter.common.model.vo.console.ConsoleDcInfoVO;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.manager.FileManager;
import net.armcloud.paascenter.openapi.model.dto.ExecuteADBDTO;
import net.armcloud.paascenter.openapi.model.dto.PadDownloadFileDTO;
import net.armcloud.paascenter.openapi.model.dto.SyncCmdDTO;
import net.armcloud.paascenter.openapi.model.vo.NetPadDeviceVO;
import net.armcloud.paascenter.openapi.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.THE_PATH_IS_INCORRECT;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Slf4j
@RestController
public class PadInternalController implements PadInternalFacade {
    @Resource
    private IPadService padService;
    @Resource
    private IPadStatusService padStatusService;
    @Resource
    private IPadConnectService padConnectService;

    @Resource
    private ICustomerAccessService customerAccessService;
    @Resource
    private ICustomerUploadImageService customerUploadImageService;
    @Resource
    private FileManager fileManager;
    @Resource
    private IPadAppService padAppService;
    @Resource
    private IPadBackupService padBackupService;

    @Override
    public Result<?> updatePadStatus(@Valid @RequestBody PadStatusDTO param) {
        Pad pad = padService.getPadByPadCode(param.getPadCode());
        Boolean result = padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(param.getPadCode()), param.getPadStatus(), pad.getCustomerId(), param.getOprBusiness());
        if (result) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<?> updatePadOnline(UpdatePadOnlineDTO param) {
        padStatusService.updatePadOnline(param);
        return Result.ok();
    }

    @Override
    public Result<CustomerAccess> accessAccessKey(@Valid @RequestBody CustomerAccessDTO param) {
        CustomerAccess customerAccess = customerAccessService.getAccessByAccessKeyId(param.getAccessKeyId());
        return Result.ok(customerAccess);
    }

    @Override
    public Result<?> updatePadStreamStatus(PadStreamStatusDTO param) {
        Boolean result = padService.updatePadStreamStatusService(param.getPadCodes(), param.getStreamStatus());
        if (result) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<Pad> padInfoByCustomerId(PadCustomerDTO padCustomerDTO) {
        return Result.ok(padService.getPadInfoByCustomerId(padCustomerDTO));
    }

    @Override
    public Result<Pad> getByCloudVendor(@Valid GetPadByCloudVendorDTO dto) {
        return Result.ok(padService.getByCloudVendorTypeAndPadOutCode(dto));
    }

    public Result<List<GeneratePadTaskVO>> reset(@Valid @RequestBody ResetDTO param) {
        return Result.ok(padService.restService(param));
    }

    public Result<List<GeneratePadTaskVO>> restart(@Valid @RequestBody RestartDTO param) {
        return Result.ok(padService.restartService(param));
    }

    @Override
    public Result<PadInfoVO> padInfo(@Valid @RequestBody PadInfoDTO param) {
        return Result.ok(padService.getPadInfoByCode(param.getPadCode()));
    }

    @Override
    public Result<?> updatePadStatusAndSendPadStatusCallback(@Valid @RequestBody SendPadStatusDTO param) {
        padStatusService.updatePadStatusAndSendPadStatusCallback(param.getPadCodes(), param.getPadStatus(), param.getCustomerId(), param.getOprBusiness());
        return Result.ok();
    }


    @Override
    public Result<List<GeneratePadTaskVO>> listApp(@Valid @RequestBody PadCodesFacadeDTO param) {
        PadCodesDTO target = new PadCodesDTO();
        BeanUtil.copyProperties(param, target);
        return Result.ok(padService.listApp(target));
    }

    @Override
    public Result<List<SyncCmdVO>> syncCmd(ExecuteADBFacadeDTO param) {
        List<SyncCmdVO>  result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(param.getPadCodes())){
            return  Result.ok(result);
        };
        param.getPadCodes().forEach(item -> {
            SyncCmdDTO syncCmdDTO = new SyncCmdDTO();
            syncCmdDTO.setPadCode(item);
            syncCmdDTO.setScriptContent(param.getCommand());
            syncCmdDTO.setCustomerId(param.getCustomerId());
            result.addAll(padService.syncCmd(syncCmdDTO, SourceTargetEnum.ADMIN_SYSTEM));
        });

        return Result.ok(result);
    }

    @Override
    public Result<List<AsyncCmdVO>> asyncCmd(ExecuteADBFacadeDTO param) {
        ExecuteADBDTO target = new ExecuteADBDTO();
        BeanUtil.copyProperties(param, target);
        if (ObjectUtil.isNotEmpty(param.getCommand())) {
            target.setScriptContent(param.getCommand());
        }
        return Result.ok(padService.asyncCmd(target));
    }

    @Override
    public Result<List<Pad>> listAllPad() {
        return Result.ok(padService.listAll());
    }

    @Override
    public Result<List<GeneratePadTaskVO>> consoleUploadFilePad(PadDownloadFileV2DTO param) {
        if (isNotEmpty(param.getCustomizeFilePath()) && !param.getCustomizeFilePath().startsWith("/")) {
            return Result.fail(THE_PATH_IS_INCORRECT);
        }
        PadDownloadFileDTO dto = new PadDownloadFileDTO();
        return Result.ok(padService.uploadFileV2(param));
    }

    @Override
    public Result<List<ConsoleDcInfoVO>> getDcIdGroupByPadCodes(List<String> padCodes) {
        log.error("PadInternalController getDcIdGroupByPadCodes ????????????????");
        return Result.ok();
//        return Result.ok(padService.getDcIdGroupByPadCodes(padCodes));
    }

    @Override
    public Result<Pad> getPadByOutCodeAndIp(PadIpDTO param) {
        return Result.ok(padService.getPadByOutCodeAndIp(param.getPadOutCode(), param.getIp()));
    }

    @Override
    public Result<?> uploadImage(@Valid @RequestBody UploadImageDTO param) {
        return Result.ok(customerUploadImageService.uploadImageContainer(param));
    }

    @Override
    public Result<List<GeneratePadTaskInfoVO>> upgradeImage(@Valid @RequestBody UpgradeImageDTO param) {
        return Result.ok(padService.upgradeImageService(param,SourceTargetEnum.ADMIN_SYSTEM));
    }

    @Override
    public Result<?> sshOrAdbConnect(SshAdbConnectDTO param) {
        return padConnectService.sshOrAdbConnect(param);
    }

    @Override
    public Result<List<GeneratePadTaskVO>> installApp(List<PadDownloadAppFileDTO> params) {
        List<GeneratePadTaskVO> result = new ArrayList<>();
        params.forEach(param -> {
            fileManager.getCustomerAppFile(param.getCustomerId(), param.getAppId(), param.getAppName(), param.getPkgName());
            List<GeneratePadTaskVO> padTaskVOS = padAppService.installApp(param);
            result.addAll(padTaskVOS);
        });
        return Result.ok(result);
    }

    @Override
    public Result<List<GeneratePadTaskVO>> uploadFileV2(@Valid @RequestBody PadDownloadFileV2DTO param) {
        if (isNotEmpty(param.getCustomizeFilePath()) && !param.getCustomizeFilePath().startsWith("/")) {
            return Result.fail(THE_PATH_IS_INCORRECT);
        }
        return Result.ok(padService.uploadFileV2(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> uninstallApp(List<PadUninstallAppFileDTO> params) {
        List<GeneratePadTaskVO> result = new ArrayList<>();
        params.forEach(param -> {
            //管理平台卸载app无需校验文件是否存在
            if(!SourceTargetEnum.ADMIN_SYSTEM.equals(param.getTaskSource())){
                fileManager.getCustomerAppFile(param.getCustomerId(), param.getAppId(), param.getAppName(), param.getPkgName());
            }
            List<GeneratePadTaskVO> padTaskVOS = padAppService.uninstallApp(param);
            result.addAll(padTaskVOS);
        });
        return Result.ok(result);
    }

    @Override
    public Result<List<GeneratePadTaskVO>> newPads(NewPadsDTO param) {
        return Result.ok(padService.newPads(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> startApp(PadAppPackageNameDTO param) {
        return Result.ok(padAppService.startApp(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> stopApp(PadAppPackageNameDTO param) {
        return Result.ok(padAppService.stopApp(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> restartApp(PadAppPackageNameDTO param) {
        return Result.ok(padAppService.restartApp(param));
    }

    @Override
    public Result<List<PadInstalledAppVO>> listInstalledApp(PadCodesDTO param) {
        return Result.ok(padAppService.listInstalledApp(param));
    }

    @Override
    public Result<?> updatePadBandwidth(PadBandwidthDTO param) {
        Boolean result = padService.updatePadBandwidthService(param);
        if (result) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<?> limitBandwidth(LimitBandwidthDTO param) {
        return Result.ok(padService.limitBandwidthService(param));
    }

    @Override
    public Result<List<GeneratePadBackupTaskVO>> backup(@Validated PadBackupDTO param) {
        return Result.ok(padBackupService.backup(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> restore(PadRestoreDTO param) {
        return Result.ok(padBackupService.restore(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> triggeringBlacklist(TriggeringBlackDTO param) {
        return Result.ok(padService.triggeringBlackListService(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> triggeringWhitelist(TriggeringBlackDTO param) {
        return Result.ok(padService.triggeringWhiteListService(param));
    }

    @Override
    public Result<PadAdbVO> padAdbConnect(PadAdbDTO param) {
        return Result.ok(padConnectService.padAdbConnect(param));
    }

    @Override
    public Result<List<GeneratePadTaskVO>> replacePad(ReplacePadTaskDTO param) {

        return Result.ok(padService.replacePad(param,SourceTargetEnum.ADMIN_SYSTEM));
    }

    @Override
    public Result<?> updatePadType(PadTypeDTO padTypeDTO) {
        Boolean result = padService.updatePadTypeService(padTypeDTO);
        if (result) {
            return Result.ok();
        }
        return Result.fail();
    }

    /**
     * 修改实例属性
     */
    @Override
    public Result<GeneratePadTaskVO> modifyPadProperties(@Valid @RequestBody ModifyPadInformationDTO param){
        try {
            return Result.ok(padService.modifyPadProperties(param, SourceTargetEnum.ADMIN_SYSTEM));
        }catch (Exception ex){
            log.error("PadInternalController.modifyPadProperties result error",ex);
            return Result.fail(ex.getMessage());
        }
    }

    @Override
    public Result<Integer> updatePadLayoutCode(PadLayoutCodeDto param) {
        try {
            return Result.ok(padService.updatePadLayoutCode(param));
        }catch (Exception ex){
            log.error("PadInternalController.updatePadLayoutCode result error",ex);
            return Result.fail(ex.getMessage());
        }
    }

    @Override
    public Result<String> unbindTheCardInformation(PadStatusDTO param) {
        try {
            return Result.ok(padService.unbindTheCardInformation(param));
        }catch (Exception ex){
            log.error("PadInternalController.unbindTheCardInformation result error",ex);
            return Result.fail(ex.getMessage());
        }
    }

    @Override
    public Result<String> deletePadInformation(PadStatusDTO param) {
        try {
            return Result.ok(padService.deletePadInformation(param));
        }catch (Exception ex){
            log.error("PadInternalController.unbindTheCardInformation result error,param:{} ,e:{}", JSON.toJSONString(param),ex.getMessage(),ex);
            return Result.fail(ex.getMessage());
        }
    }

    @Override
    public Result<List<GeneratePadTaskVO>> netStorageResBootOn(NetWorkOnDTO param) {
        return Result.ok(padService.netStorageResBootOn(param,SourceTargetEnum.ADMIN_SYSTEM));
    }
    @Override
    public Result<List<GeneratePadTaskVO>> netStorageResBootDelete(NetWorkDeleteDTO  param) {
        return Result.ok(padService.netStorageResDelete(param,SourceTargetEnum.ADMIN_SYSTEM));
    }

    @Override
    public      Result<String> netStorageResMoreCompatible(NetStorageResMoreCompatiblePaasDTO param) {
        return Result.ok(padService.netStorageResMoreCompatible(param,SourceTargetEnum.ADMIN_SYSTEM));
    }



    @Override
    public Result<List<GeneratePadTaskVO>> netStorageResBootOff(NetWorkOffDTO param) {
        return Result.ok(padService.netStorageResBootOff(param,SourceTargetEnum.ADMIN_SYSTEM));
    }

    @Override
    public Result<String> virtualizeNetStorageRes(NetWorkVirtualizeDTO param) {
        padService.virtualizeNetStorageRes(param,SourceTargetEnum.ADMIN_SYSTEM);
        return Result.ok();
    }


    @Override
    public Result<List<NetPadDeviceVO>> groupNetPadByDeviceLevel(NetStorageResDetailDTO param){
        return Result.ok(padService.groupNetPadByDeviceLevel(param));
    }
}
