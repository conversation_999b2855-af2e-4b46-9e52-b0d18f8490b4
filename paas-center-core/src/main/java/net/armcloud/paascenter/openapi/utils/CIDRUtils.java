package net.armcloud.paascenter.openapi.utils;

import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.util.*;
import java.util.stream.Collectors;

public class CIDRUtils {

    /**
     * 根据 CIDR 生成 IP 地址集合
     *
     * @param cidr CIDR 格式的地址，例如 "**********/24"
     * @return IP 地址的集合，如果发生异常则返回 null
     */
    public static Set<String> getIPAddressesFromCIDR(String cidr) {
        return getIPAddressesFromCIDR(cidr,Collections.emptyList());
    }


    public static Set<String> getIPAddressesFromCIDR(String cidr, List<Integer> subNetIpWhiteList) {
        try {
            String[] parts = cidr.split("/");
            String ip = parts[0];

            int subIpNum = getSubNetNum(ip);
            int prefixLength = Integer.parseInt(parts[1]);
//            //************/17
//            if (prefixLength < 24) {
//
//            }
            InetAddress inetAddress = InetAddress.getByName(ip);
            byte[] addressBytes = inetAddress.getAddress();

            int mask = 0xffffffff << (32 - prefixLength);
            int baseAddress = toInt(addressBytes) & mask;

            List<String> ipAddresses = new ArrayList<>();
            int numberOfAddresses = (prefixLength == 32) ? 1 : (1 << (32 - prefixLength));

            for (int i = 1; i < numberOfAddresses - 1; i++) {
                int currentAddress = baseAddress + i;
                ipAddresses.add(toIPString(currentAddress));
            }
            Set<String> ipAddressesFinal = new HashSet<>();
            if (ipAddresses.size()>0) {
                for (String ipAddress : ipAddresses) {
                    if(StringUtils.isBlank(ipAddress)){
                        continue;
                    }
                    Integer subNetNum = getSubNetNum(ipAddress);
                    if (subIpNum != 0 && subNetNum >= subIpNum && !ipAddress.endsWith(".0")
                            && !ipAddress.endsWith(".255")) {
                        if (subNetIpWhiteList.size() > 0 && subNetIpWhiteList.contains(subNetNum)) {
                            ipAddressesFinal.add(ipAddress);
                        } else if(subNetIpWhiteList == null ||subNetIpWhiteList.size() == 0 ) {
                            ipAddressesFinal.add(ipAddress);
                        }
                    }
                }
            }

            return ipAddressesFinal;
        } catch (Exception e) {
            // 捕获所有异常并返回 null
            return null;
        }
    }

    private static Integer getSubNetNum(String ip) {
        // get the third  ip str
        String[] splitIps = ip.split("\\.");
        Integer ipSubNetStart = 0;
        if (splitIps.length == 4) {
            String ipSubNetStartStr = splitIps[2];
            try {
                return Integer.parseInt(ipSubNetStartStr);
            } catch (Exception e) {
                // nothing
            }
        }
        return 0;
    }

    /**
     * 将字节数组形式的 IP 地址转换为整数
     *
     * @param address 字节数组形式的 IP 地址
     * @return 整数形式的 IP 地址
     */
    private static int toInt(byte[] address) {
        int result = 0;
        for (byte b : address) {
            result = (result << 8) | (b & 0xFF);
        }
        return result;
    }

    /**
     * 将整数形式的 IP 地址转换为点分十进制字符串
     *
     * @param address 整数形式的 IP 地址
     * @return 点分十进制形式的 IP 地址字符串
     */
    private static String toIPString(int address) {
        return ((address >> 24) & 0xFF) + "." +
                ((address >> 16) & 0xFF) + "." +
                ((address >> 8) & 0xFF) + "." +
                (address & 0xFF);
    }

    /**
     * 将点分十进制表示的 IP 地址转换为整数
     *
     * @param ipAddress 点分十进制表示的 IP 地址
     * @return 整数表示的 IP 地址
     */
    public static int ipToInt(String ipAddress) {
        String[] octets = ipAddress.split("\\.");
        int result = 0;
        for (String octet : octets) {
            result = (result << 8) | Integer.parseInt(octet);
        }
        return result;
    }

    /**
     * 对 IP 地址集合进行排序
     *
     * @param ips IP 地址集合
     * @return 排序后的 IP 地址集合
     */
    public static List<String> sortIPs(List<String> ips) {
        return ips.stream()
                .sorted(Comparator.comparingInt(CIDRUtils::ipToInt))
                .collect(Collectors.toList());
    }
//
//    public static void main(String[] args) {
//        List<String> ipAddressesFromCIDR = getIPAddressesFromCIDR("************/24");
//        for (String s : ipAddressesFromCIDR) {
//            System.out.print(s + " ");
//        }
//        System.out.println(ipAddressesFromCIDR.size());
//    }

}