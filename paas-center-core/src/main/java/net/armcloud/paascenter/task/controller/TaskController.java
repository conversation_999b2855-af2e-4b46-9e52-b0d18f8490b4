package net.armcloud.paascenter.task.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.TaskDTO;
import net.armcloud.paascenter.common.model.dto.api.TaskDetailsInfoDTO;
import net.armcloud.paascenter.common.model.dto.api.TaskImageUploadDTO;
import net.armcloud.paascenter.common.model.vo.api.*;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.manager.TaskQueueManager;
import net.armcloud.paascenter.task.model.dto.PullEdgeClusterConfigurationDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskHealthDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskResultDTO;
import net.armcloud.paascenter.task.model.vo.PullTaskVO;
import net.armcloud.paascenter.task.service.IPullTaskService;
import net.armcloud.paascenter.task.service.ITaskService;
import net.armcloud.paascenter.task.service.impl.ImageTaskServerImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;
import static net.armcloud.paascenter.task.exception.code.TaskExceptionCode.TASK_TYPE_NOT_EXIST;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Slf4j
@RestController
@RequestMapping("/task-center/open/task")
@Api(tags = {"任务接口", "功能接口"}, description = "用于处理任务逻辑")
public class TaskController {
	@Resource
	private ITaskService taskService;

	@Resource
	private ImageTaskServerImpl imageTaskServer;
	@Resource
	private IPullTaskService pullTaskService;


	/**
	 * 获取实例操作任务详情
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/padTaskDetail")
	@ApiOperation(value = "获取实例操作任务详情", notes = "获取实例操作任务详情")
	public Result<List<PadTaskViewVO>> padTaskDetail(@Valid @RequestBody TaskDetailsInfoDTO taskDetailsDTO) {
		taskDetailsDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
		List<PadTaskViewVO> padTaskDetailsService = taskService.padTaskDetailsService(taskDetailsDTO);
//        log.info("获取实例操作任务详情,入参:{},出参:{}", JSON.toJSONString(taskDetailsDTO),JSON.toJSONString(padTaskDetailsService));
		return Result.ok(padTaskDetailsService);
	}

	/**
	 * 获取文件任务详情
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/fileTaskDetail")
	@ApiOperation(value = "获取文件任务详情", notes = "获取文件任务详情")
	public Result<List<FileTaskViewVO>> fileTaskDetail(@Valid @RequestBody TaskDetailsInfoDTO taskDetailsDTO) {
		taskDetailsDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
		return Result.ok(taskService.fileTaskDetailsService(taskDetailsDTO));
	}

	/**
	 * 获取实例执行脚本结果
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/executeScriptInfo")
	@ApiOperation(value = "获取实例执行脚本结果", notes = "获取实例执行脚本结果")
	public Result<List<PadTaskViewVO>> executeScriptInfo(@Valid @RequestBody TaskDetailsInfoDTO taskDetailsDTO) {
		taskDetailsDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
		taskDetailsDTO.setTypes(Collections.singletonList(EXECUTE_COMMAND.getType()));
		return Result.ok(taskService.padTaskDetailsService(taskDetailsDTO));
	}

	/**
	 * 获取实例截图结果
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/screenshotInfo")
	@ApiOperation(value = "获取实例截图结果", notes = "获取实例截图结果")
	public Result<List<PadTaskViewVO>> screenshotInfo(@Valid @RequestBody TaskDetailsInfoDTO taskDetailsDTO) {
		taskDetailsDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
		taskDetailsDTO.setTypes(Collections.singletonList(SCREENSHOT_LOCAL.getType()));
		return Result.ok(taskService.padTaskDetailsService(taskDetailsDTO));
	}


	/**
	 * 应用启停执行结果
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/appOperateInfo")
	@ApiOperation(value = "应用启停执行结果", notes = "应用启停执行结果")
	public Result<List<PadTaskViewVO>> appOperateInfo(@Valid @RequestBody TaskDetailsInfoDTO taskDetailsDTO) {
		taskDetailsDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
		taskDetailsDTO.setTypes(new ArrayList<>(Arrays.asList(STOP_APP.getType(), START_APP.getType())));
		return Result.ok(taskService.padTaskDetailsService(taskDetailsDTO));
	}

	/**
	 * 实例重启重置执行结果
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/padExecuteTaskInfo")
	@ApiOperation(value = "实例重启重置执行结果", notes = "实例重启重置执行结果")
	public Result<List<PadTaskViewVO>> padExecuteTaskInfo(@Valid @RequestBody TaskDetailsInfoDTO taskDetailsDTO) {
		taskDetailsDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
		taskDetailsDTO.setTypes(new ArrayList<>(Arrays.asList(RESTART.getType(), RESET.getType())));
		return Result.ok(taskService.padTaskDetailsService(taskDetailsDTO));
	}

	/**
	 * 镜像上传任务结果查询
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/imageUploadResult")
	@ApiOperation(value = "镜像上传任务结果查询", notes = "镜像上传任务结果查询")
	public Result<List<TaskImageUploadVo>> imageUploadResult(@Valid @RequestBody TaskImageUploadDTO taskDetailsDTO) {
		taskDetailsDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
		List<TaskImageUploadVo> list = imageTaskServer.taskList(taskDetailsDTO);
		return Result.ok(list);
	}


	/**
	 * 拉模式 - 获取待执行的任务列表
	 */
	@PostMapping(value = "/pull/list")
	@ApiOperation(value = "拉模式 - 获取待执行的任务列表", notes = "拉模式 - 获取待执行的任务列表")
	public Result<PullTaskVO> taskList(@Valid @RequestBody PullTaskDTO param) {
		log.debug("获取待执行的任务列表:{}", JSON.toJSONString(param));
		return Result.ok(pullTaskService.taskList(param));
	}


	@GetMapping(value = "/pull/getTest")
	public Result<PullTaskVO> getTest() {
		log.debug("getTest");
		return Result.ok();
	}

	/**
	 * 拉模式 - 上报任务结果
	 */
	@PostMapping(value = "/pull/submit/result")
	@ApiOperation(value = "拉模式 - 上报任务结果", notes = "拉模式 - 上报任务结果")
	public Result<?> submitResult(@Valid @RequestBody PullTaskResultDTO param) {
		try {
			if (StrUtil.isNotBlank(param.getCommand())) {
				log.info("上报任务结果参数command为空 clusterCode:{},deviceIp:{},deviceType:{},taskId:{},taskStatus:{},errMsg:{},command:{}," +
                                "padCode:{},taskType:{}",
						param.getClusterCode(), param.getDeviceIp(), param.getDeviceType(), param.getTaskId(), param.getTaskStatus(),
                        param.getErrMsg(),param.getCommand(),param.getDeviceCode(), param.getTaskType());
			} else {
				log.info("上报任务结果参数:{}", JSON.toJSONString(param));
			}
			pullTaskService.submitResultDirect(param);
		} catch (Exception e) {
			log.error("上报任务结果异常，请求参数{}，异常{}", JSON.toJSONString(param), e);
			return Result.fail("上报失败");
		}
		return Result.ok();
	}

	/**
	 * 拉模式 - 健康状态上报
	 */
	@PostMapping(value = "/pull/health")
	@ApiOperation(value = "拉模式 - 健康状态上报", notes = "拉模式 - 健康状态上报")
	public Result<?> pullHealth(@Valid @RequestBody PullTaskHealthDTO param) {
		try {
			log.info("健康状态上报参数:{}", JSON.toJSONString(param));
			pullTaskService.pullHealth(param);
		} catch (Exception e) {
			log.error("健康状态上报异常，请求参数{}，异常{}", JSON.toJSONString(param), e);
			return Result.fail("上报失败");
		}
		return Result.ok();
	}

	@PostMapping(value = "/gs/edgeClusterConfiguration")
	@ApiOperation(value = "拉模式 - 集群配置信息查询 - gameserver", notes = "拉模式 - 集群配置信息查询 - gameserver")
	public Result<Map<String, String>> gsEdgeClusterConfiguration(@Valid @RequestBody PullEdgeClusterConfigurationDTO param) {
		param.setDeviceType(TaskChannelEnum.GAMESERVER.getCode());
		return Result.ok(pullTaskService.edgeClusterConfiguration(param));
	}

	@PostMapping(value = "/device/edgeClusterConfiguration")
	@ApiOperation(value = "拉模式 - 集群配置信息查询 - cbs和bmc", notes = "拉模式 - 集群配置信息查询 - cbs和bmc")
	public Result<Map<String, String>> deviceEdgeClusterConfiguration(@Valid @RequestBody PullEdgeClusterConfigurationDTO param) {
		return Result.ok(pullTaskService.edgeClusterConfiguration(param));
	}
}
