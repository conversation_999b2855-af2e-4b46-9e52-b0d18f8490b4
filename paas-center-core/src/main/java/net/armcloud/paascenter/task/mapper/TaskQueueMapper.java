package net.armcloud.paascenter.task.mapper;

import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface TaskQueueMapper {
    int insert(TaskQueue record);

    /**
     * 如果是批量的任务根据masterTaskId会查询到多条记录导致异常，需要增加padCode条件
     * 获取任务内容通过主任务ID
     * @param masterTaskId
     * @return
     */
    String selectContentJsonOne(@Param("masterTaskId") Long masterTaskId,@Param("key") String key);

    void updateStatusByKeyAndSubtaskId(@Param("key") String key, @Param("subTaskId") long subTaskId, @Param("status") int status);


    TaskQueue getByMasterAndSubTaskId(@Param("masterTaskId") long masterTaskId, @Param("subTaskId") long subTaskId);


}