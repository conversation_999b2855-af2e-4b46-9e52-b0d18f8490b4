package net.armcloud.paascenter.task.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 拉取任务结果上报请求对象
 */
@Data
public class PullTaskResultDTO {
    /**集群编号*/
    private String clusterCode;
    /**设备ip*/
    private String deviceIp;
    /**设备类型 GS、CBS、BMC*/
    @NotEmpty(message = "设备类型不能为空")
    private String deviceType;
    /**任务ID*/
    private Long taskId;
    /**任务状态*/
    @NotNull(message = "任务状态不能为空")
    private Integer taskStatus;
    /**异常消息*/
    private String errMsg;

    /**任务结果*/
    private String result;

    /**指令 gameserver填写*/
    private String command;

    /**设备编号*/
    private String deviceCode;
    /**任务类型 cbs和bmc传递*/
    private Integer taskType;

}
