<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.ArmServerMapper">

    <sql id="Base_Column_List">
        id, code, soc_model, cluster_code,arm_server_code, arm_server_name, arm_sn, arm_ip, device_subnet,
     `status`, `online`, remarks, delete_flag, create_by, create_time, update_by,update_time,gateway_device_id,gateway_pad_id
    </sql>
    <select id="listByArmServerDTO" resultType="net.armcloud.paascenter.openapi.model.vo.ArmServerVo">
        SELECT
        a.cluster_code,
        a.arm_server_code,
        a.arm_server_name,
        a.arm_sn,
        a.arm_ip,
        a.device_subnet,
        a.STATUS,
        a.ONLINE,
        a.mac_vlan,
        a.chassis_label,
        a.chassis_cabinet_u,
        a.gateway_pad_id,
        di.id as idc,
        a.gateway_device_id
        FROM
        arm_server a
        inner join edge_cluster ec on a.cluster_code = ec.cluster_code
        inner join  dc_info  di on di.dc_code = ec.dc_code
        INNER JOIN ( SELECT DISTINCT arm_server_code FROM pad WHERE customer_id = #{customerId} ) b ON a.arm_server_code = b.arm_server_code
        WHERE
        a.delete_flag = 0
        <if test="clusterCode != null and clusterCode != '' ">
            and a.cluster_code  like CONCAT('%',#{clusterCode},'%')
        </if>
        <if test="idc != null and idc != '' ">
            and di.id = #{idc}
        </if>
        <if test="armServerCode != null and armServerCode != '' ">
            and a.arm_server_code like CONCAT('%',#{armServerCode},'%')
        </if>
        <if test="armServerName != null and armServerName != '' ">
            and a.arm_server_name like CONCAT('%',#{armServerName},'%')
        </if>
        <if test="armSn != null and armSn != '' ">
            and a.arm_sn like CONCAT('%',#{armSn},'%')
        </if>
        <if test="armIp != null and armIp != '' ">
            and a.arm_ip like CONCAT('%',#{armIp},'%')
        </if>
        <if test="online != null and online != '' ">
            and a.online like CONCAT('%',#{online},'%')
        </if>
    </select>
    <select id="selectByArmIp" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where arm_ip = #{serverIp} and delete_flag = 0
    </select>
    <select id="existChassisLabel" resultType="java.lang.Integer">
        select
            count(1)
        from arm_server
        where chassis_label = #{chassisLabel} and delete_flag = 0
    </select>
    <select id="selectByArmServerCode" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where arm_server_code = #{armServerCode} and delete_flag = 0
    </select>

    <select id="armIpList" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        SELECT arm.* FROM `arm_server` arm
        left join edge_cluster edg on arm.cluster_code = edg.cluster_code
        left join dc_info dc on dc.dc_code = edg.dc_code
        where dc.delete_flag = 0 and edg.delete_flag = 0 and edg.status = 1
        and arm.delete_flag = 0 and arm.status = 1
        and arm.arm_ip in
        <foreach collection="armIpList" item="armIp" open="(" separator="," close=")">
            #{armIp}
        </foreach>
    </select>
    <update id="updateArmServer" parameterType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        update arm_server
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="socModel != null">
                soc_model = #{socModel,jdbcType=BIGINT},
            </if>
            <if test="clusterCode != null">
                cluster_code = #{clusterCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerCode != null">
                arm_server_code = #{armServerCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerName != null">
                arm_server_name = #{armServerName,jdbcType=VARCHAR},
            </if>
            <if test="armSn != null">
                arm_sn = #{armSn,jdbcType=VARCHAR},
            </if>
            <if test="armIp != null">
                arm_ip = #{armIp,jdbcType=VARCHAR},
            </if>
            <if test="deviceSubnet != null">
                device_subnet = #{deviceSubnet,jdbcType=VARCHAR},
            </if>
            <if test="netServerId != null">
                net_server_id = #{netServerId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="online != null">
                `online` = #{online,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gatewayDeviceId != null">
                gateway_device_id = #{gatewayDeviceId,jdbcType=VARCHAR},
            </if>
            <if test="gatewayPadId != null">
                gateway_pad_id = #{gatewayPadId,jdbcType=VARCHAR},
            </if>
            <if test="chassisCabinetU != null">
                chassis_cabinet_u = #{chassisCabinetU,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveArmServer" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.ArmServer" useGeneratedKeys="true">
        insert into arm_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">
                code,
            </if>
            <if test="socModel != null">
                soc_model,
            </if>
            <if test="clusterCode != null">
                cluster_code,
            </if>
            <if test="armServerCode != null">
                arm_server_code,
            </if>
            <if test="armServerName != null">
                arm_server_name,
            </if>
            <if test="armSn != null">
                arm_sn,
            </if>
            <if test="armIp != null">
                arm_ip,
            </if>
            <if test="deviceSubnet != null">
                device_subnet,
            </if>
            <if test="netServerId != null">
                net_server_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="online != null">
                `online`,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="gatewayDeviceId != null">
                gateway_device_id,
            </if>
            <if test="gatewayPadId != null">
                gateway_pad_id,
            </if>
            <if test="macVlan != null">
                mac_vlan,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="chassisLabel != null">
                chassis_label,
            </if>
            <if test="chassisCabinetU != null">
                chassis_cabinet_u,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="socModel != null">
                #{socModel,jdbcType=BIGINT},
            </if>
            <if test="clusterCode != null">
                #{clusterCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerCode != null">
                #{armServerCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerName != null">
                #{armServerName,jdbcType=VARCHAR},
            </if>
            <if test="armSn != null">
                #{armSn,jdbcType=VARCHAR},
            </if>
            <if test="armIp != null">
                #{armIp,jdbcType=VARCHAR},
            </if>
            <if test="deviceSubnet != null">
                #{deviceSubnet,jdbcType=VARCHAR},
            </if>
            <if test="netServerId != null">
                #{netServerId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="online != null">
                #{online,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gatewayDeviceId != null">
                #{gatewayDeviceId,jdbcType=VARCHAR},
            </if>
            <if test="gatewayPadId != null">
                #{gatewayPadId,jdbcType=VARCHAR},
            </if>
            <if test="macVlan != null">
                #{macVlan,jdbcType=VARCHAR},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=TINYINT},
            </if>
            <if test="chassisLabel != null">
                #{chassisLabel,jdbcType=VARCHAR},
            </if>
            <if test="chassisCabinetU != null">
                #{chassisCabinetU,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="existArmIp" resultType="int">
        select
            count(1)
        from arm_server
        where arm_ip = #{serverIp} and delete_flag = 0
    </select>
    <select id="existArmServerCode" resultType="int">
        select
            count(1)
        from arm_server
        where arm_server_code = #{armServerCode}
    </select>

    <select id="selectBrandInfoByDeviceCode" resultType="net.armcloud.paascenter.task.model.vo.BmcBrandInfo">
        SELECT arm_bmc_api_uri, brand_id, bmc_account, bmc_password
        FROM arm_server a
                 left join device b
                           on a.arm_server_code = b.arm_server_code
        where b.device_code = #{deviceCode}
          and b.delete_flag = 0
          and a.delete_flag = 0
    </select>

    <select id="selectBrandInfoByArmServer" resultType="net.armcloud.paascenter.task.model.vo.BmcBrandInfo">
        SELECT arm_bmc_api_uri, brand_id, bmc_account, bmc_password
        FROM arm_server
        where
            arm_server_code = #{armServerCode}
          and delete_flag = 0
    </select>



</mapper>
